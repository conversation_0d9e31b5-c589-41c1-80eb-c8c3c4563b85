
// 获取安装按钮
//const installButton = document.getElementById('InstallBtn');
//console.log('installButton ' + installButton);

let deferredPrompt;

window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;
});

installButton.addEventListener('click', (e) => {
	console.log('installButtonclick');
    deferredPrompt.prompt();
    deferredPrompt.userChoice.then((choiceResult) => {
        if (choiceResult.outcome === 'accepted') {
			console.log('User accepted the install prompt');
                
		} else {
			console.log('User dismissed the install prompt');
		}
		deferredPrompt = null;
	});
        
});


window.installButtonclick = function(){
	console.log('installButtonclick');
    deferredPrompt.prompt();
    deferredPrompt.userChoice.then((choiceResult) => {
        if (choiceResult.outcome === 'accepted') {
			console.log('User accepted the install prompt');
                
		} else {
			console.log('User dismissed the install prompt');
		}
		deferredPrompt = null;
	});
	
}


window.addEventListener('appinstalled', (evt) => {
    window.open('/index.html', '_self');
});
