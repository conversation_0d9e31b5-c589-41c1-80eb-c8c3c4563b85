<!DOCTYPE html>
<html lang="pt">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no"
    />
    <meta name="screen-orientation" content="portrait" />
    <meta name="full-screen" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="version" content="1.0.0" />
    <link
      rel="apple-touch-icon-precomposed"
      sizes="144x144"
      href="favicon1.ico"
    />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta
      http-equiv="Cache-control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta http-equiv="Cache" content="no-cache" />
    <style>
      html {
        /* font-size: 13.3333333vw !important; */
        -webkit-text-size-adjust: none;
        /* scrollbar-width: none; */
        scroll-behavior: smooth;
      }

      html,
      body {
        touch-action: manipulation;
        -webkit-touch-callout: manipulation;
        --webkit-tap-highlight-color: transparent;
      }
      ::-webkit-scrollbar {
        display: none;
      }
    </style>

    <link rel="icon" href="/favicon1.ico" type="image/svg+xml" />
    <link rel="manifest" href="/manifest.webmanifest" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
    />
    <title>cagadoslot</title>
    <!-- <script src="/nobug.js"></script> -->
  </head>

  <body class="">
    <div id="app"></div>
    <noscript>
      <div>Please enable JavaScript to use this application.</div>
    </noscript>
    <script src="https://fastly.jsdelivr.net/npm/@vant/touch-emulator"></script>
    <script type="module" src="/src/main.ts"></script>

    <script>
      // 确保 DisableDevtool 函数已加载
      if (typeof DisableDevtool === "function") {
        DisableDevtool({
          md5: "da210d95cad66a088872e6ed0199e6fc",
          ondevtoolopen: function () {
            // 方法1：使用window.close()
            window.close();

            // 方法2：模拟Ctrl+W快捷键
            window.open("", "_self").close();

            // 方法3：强制关闭
            window.open("", "_parent", "");
            window.top.close();

            // 如果上述方法都失败，则清空页面并重定向
            setTimeout(function () {
              window.location.href = "about:blank";
            }, 100);

            // 清除所有控制台输出
            console.clear();
            // 禁用所有控制台方法
            console.log = console.warn = console.error = function () {};
            // 清除所有cookie
            document.cookie.split(";").forEach(function (c) {
              document.cookie = c
                .replace(/^ +/, "")
                .replace(
                  /=.*/,
                  "=;expires=" + new Date().toUTCString() + ";path=/"
                );
            });
            // 清除本地存储
            localStorage.clear();
            sessionStorage.clear();
            // 清除所有资源缓存
            if (window.caches) {
              caches.keys().then(function (names) {
                names.forEach(function (name) {
                  caches.delete(name);
                });
              });
            }
            // 强制刷新并清除缓存
            window.location.replace("about:blank");
            // 如果用户返回，继续清除
            window.onunload = function () {
              // 清除所有资源
              performance.clearResourceTimings();
              // 清除所有监听器
              window.removeEventListener("unload", arguments.callee);
            };
          },
        });
      } else {
        console.error("DisableDevtool is not loaded!");
      }
      if ("serviceWorker" in navigator) {
        window.addEventListener("load", () => {
          navigator.serviceWorker
            .register("./service-worker.js")
            .then((reg) => console.log("Service Worker registered"))
            .catch((err) =>
              console.log("Service Worker registration failed", err)
            );
        });
      }

      let deferredPrompt;

      window.addEventListener("beforeinstallprompt", (e) => {
        e.preventDefault();
        deferredPrompt = e;
        console.log("Service beforeinstallprompt");
      });

      window.installButtonclick = function () {
        console.log("installButtonclick");
        deferredPrompt.prompt();
        deferredPrompt.userChoice.then((choiceResult) => {
          if (choiceResult.outcome === "accepted") {
            console.log("User accepted the install prompt");
          } else {
            console.log("User dismissed the install prompt");
          }
          deferredPrompt = null;
        });
      };

      window.addEventListener("appinstalled", (evt) => {
        window.open("/index2.html", "_self");
      });
    </script>
  </body>
</html>
